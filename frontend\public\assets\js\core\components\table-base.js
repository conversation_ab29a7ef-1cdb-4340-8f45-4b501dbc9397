/*
 * Base Table Class - Simplified
 * Foundation for all table implementations in Sed-LIMS
 * Simplified for GC Web Standards compliance without over-engineering
 */
import { showMessage } from '../helpers/message-helpers.js';
import { getLocalizedText } from '../i18n/i18n-helpers.js';
import { initializeWETTable } from '../helpers/table-helpers.js';
import { escapeHtml, formatDate, formatNotAvailable, getRoleDisplayName } from '../helpers/format-helpers.js';
import { GLOBAL_CONFIGS } from '../config/global-configs.js';

export class BaseTable {
    constructor(config) {
        this.containerId = config.containerId;
        this.tableId = config.tableId;
        this.entityConfig = config.entityConfig;
        this.messageConfigMaps = config.messageConfigMaps;
    }

    // Standard table creation flow
    async create(config = {}) {
        // Simple configuration override
        if (config.containerId) this.containerId = config.containerId;
        if (config.tableId) this.tableId = config.tableId;
        if (config.userInfo) this.userInfo = config.userInfo;

        const $container = $(`#${this.containerId}`);

        if (!$container.length) {
            console.warn(`Table container not found: ${this.containerId}`);
            throw new Error('Container not found');
        }

        try {
            // Show loading state
            this.showLoadingState($container);

            // Fetch data (implemented by subclasses)
            const data = await this.fetchData();

            // Handle empty data
            if (!data || data.length === 0) {
                this.showEmptyState($container);
                return;
            }

            // Build and display table
            const tableHtml = this.buildTable(data);
            $container.html(tableHtml);

            // Initialize WET-BOEW DataTables
            await initializeWETTable(this.tableId);

            // Post-initialization setup (optional, implemented by subclasses)
            this.postInitialize();

        } catch (error) {
            this.handleError($container, error);
            throw error;
        }
    }

    // Show loading state with accessibility
    showLoadingState($container) {
        const loadingText = getLocalizedText({ message: GLOBAL_CONFIGS.ui.common.loading }, 'message');
        $container.html(`<p role="status" aria-live="polite">${loadingText}...</p>`);
    }

    // Show empty state with localized message
    showEmptyState($container) {
        const entityTerm = getLocalizedText({ message: this.entityConfig.entityTerms }, 'message');
        const emptyTableTemplate = getLocalizedText({ message: GLOBAL_CONFIGS.ui.table.messageTemplates.emptyTable }, 'message');
        const emptyTableText = emptyTableTemplate.replace('{entity}', entityTerm);
        $container.html(`<div class="alert alert-info"><p>${emptyTableText}</p></div>`);
    }

    // Handle errors with standard GC error display
    handleError($container, error) {
        $container.empty();
        console.error(`Failed to create table "${this.tableId}":`, error);
        showMessage('#page-error-container', 'global.messages.errors.server', 'connectionError', this.messageConfigMaps);
    }

    // Build table using schema-driven approach
    buildTable(data) {
        // All tables must use schema-driven approach
        if (!this.entityConfig.tableSchema || !this.entityConfig.tableSchema.columns) {
            throw new Error(`Table "${this.tableId}" must define tableSchema in entity configuration`);
        }

        return this.buildTableFromSchema(data);
    }

    // Schema-driven table building - can be overridden by subclasses
    buildTableFromSchema(data) {
        const columns = this.getVisibleColumns();

        // Build headers
        const headerCells = columns.map(column => {
            const headerText = getLocalizedText({ message: column.header }, 'message');
            return `<th scope="col">${headerText}</th>`;
        }).join('');

        // Build rows
        const tableRows = data.map(row => {
            const cells = columns.map(column => {
                const cellValue = this.buildCellValue(column, row);
                return `<td>${cellValue}</td>`;
            }).join('');
            return `<tr>${cells}</tr>`;
        }).join('');

        return this.buildFinalTableHtml(headerCells, tableRows);
    }

    // Get visible columns - can be overridden for filtering
    getVisibleColumns() {
        return this.entityConfig.tableSchema.columns;
    }

    // Build cell value with simplified logic
    buildCellValue(column, row) {
        // Get raw value - use column.key by default, accessor if provided
        const rawValue = column.accessor ? column.accessor(row) : row[column.key];

        // Apply formatter if specified
        if (column.formatter) {
            return this.applyFormatter(column.formatter, rawValue, row);
        }

        // Default: escape HTML
        return escapeHtml(rawValue || '');
    }

    // Build final table HTML directly (no helper abstraction)
    buildFinalTableHtml(headerCells, tableRows) {
        const schema = this.entityConfig.tableSchema;
        const schemaOptions = schema.defaultOptions || {};
        const customOptions = this.getTableOptions();
        const columnDefs = this.buildColumnDefs();

        // Build message config directly
        const entityTerm = getLocalizedText({ message: this.entityConfig.entityTerms }, 'message');
        const templates = GLOBAL_CONFIGS.ui.table.messageTemplates;

        // Standard WET-BOEW table configuration
        const tableOptions = {
            ordering: true,
            order: schemaOptions.order || [[0, "asc"]],
            paging: true,
            pageLength: 10,
            lengthMenu: [[10, 25, 50, -1], [10, 25, 50, getLocalizedText({ message: GLOBAL_CONFIGS.ui.table.all }, 'message')]],
            info: true,
            searching: true,
            columnDefs: columnDefs,
            language: {
                emptyTable: getLocalizedText({ message: templates.emptyTable }, 'message').replace('{entity}', entityTerm),
                search: getLocalizedText({ message: templates.search }, 'message').replace('{entity}', entityTerm),
                lengthMenu: getLocalizedText({ message: templates.lengthMenu }, 'message').replace('{entity}', entityTerm),
                info: getLocalizedText({ message: templates.info }, 'message').replace('{entity}', entityTerm)
            },
            ...customOptions
        };

        // Ensure proper JSON escaping for HTML attributes
        const dataWbTables = JSON.stringify(tableOptions)
            .replace(/'/g, '&apos;')
            .replace(/"/g, '&quot;');

        return `
            <div class="table-responsive">
                <table id="${this.tableId}" class="table table-striped table-hover wb-tables"
                    data-wb-tables="${dataWbTables}">
                    <thead><tr>${headerCells}</tr></thead>
                    <tbody>${tableRows}</tbody>
                </table>
            </div>
        `;
    }

    // Build column definitions for DataTables
    buildColumnDefs() {
        const columnDefs = [];
        const columns = this.getVisibleColumns();

        // Ensure we have valid columns
        if (!columns || columns.length === 0) {
            return columnDefs;
        }

        columns.forEach((column, index) => {
            if (column.sort === false || column.orderable === false) {
                columnDefs.push({
                    targets: index, // Use single index instead of array
                    orderable: false
                });
            }
        });
        return columnDefs;
    }

    // Abstract method to be implemented by subclasses
    async fetchData() {
        throw new Error('fetchData() must be implemented by subclass');
    }

    // Optional methods that can be overridden
    getTableOptions() {
        return {}; // Default empty options
    }

    postInitialize() {
        // Optional post-initialization logic
    }

    // Apply formatter to value - simplified system
    applyFormatter(formatterName, value, row) {
        // Check entity-specific formatters first (most common)
        if (this.entityFormatters && this.entityFormatters[formatterName]) {
            return this.entityFormatters[formatterName](value, row);
        }

        // Check static formatters
        if (BaseTable.formatters[formatterName]) {
            return BaseTable.formatters[formatterName](value, row);
        }

        console.warn(`Formatter not found: ${formatterName}`);
        return escapeHtml(value || '');
    }

    // Static formatters for common use cases
    static formatters = {
        safeText: (value) => escapeHtml(value || formatNotAvailable()),
        formatDate: (value) => value ? formatDate(value) : formatNotAvailable(),
        formatDateWithFallback: (value, row) => {
            const dateToUse = value || row.updated_at || row.created_at;
            return dateToUse ? formatDate(dateToUse) : formatNotAvailable();
        },
        roleDisplay: (value) => getRoleDisplayName(value)
    };
}

// Read-only table class
export class ReadOnlyTable extends BaseTable {
    constructor(config) {
        super(config);
    }

    postInitialize() {
        // No additional setup needed for read-only tables
    }
}

// Bulk checkbox table class - simplified
export class BulkCheckboxTable extends BaseTable {
    constructor(config) {
        super(config);
        this.hasChanges = false;
    }

    // Override to replace last column with checkbox
    getVisibleColumns() {
        const columns = [...this.entityConfig.tableSchema.columns];
        
        // Replace last column with checkbox column for bulk operations
        if (columns.length > 0) {
            const lastColumnIndex = columns.length - 1;
            const originalColumn = columns[lastColumnIndex];
            
            columns[lastColumnIndex] = {
                key: 'actions',
                header: originalColumn.header, // Reuse original header (usually Status)
                sort: false,
                orderable: false
            };
        }
        
        return columns;
    }

    // Override cell building to handle checkbox column
    buildCellValue(column, row) {
        if (column.key === 'actions') {
            return this.buildCheckboxCell(row);
        }
        return super.buildCellValue(column, row);
    }

    // Override to add bulk actions
    buildFinalTableHtml(headerCells, tableRows) {
        const tableHtml = super.buildFinalTableHtml(headerCells, tableRows);
        const bulkActionsHtml = this.buildBulkActionsHtml();
        return tableHtml + bulkActionsHtml;
    }

    // Build checkbox cell HTML (to be implemented by subclasses)
    buildCheckboxCell(row) {
        return `<input type="checkbox" data-id="${row.id || row.test_type_id}">`;
    }

    // Build bulk actions HTML (to be implemented by subclasses)
    buildBulkActionsHtml() {
        return '';
    }

    // Bulk checkbox tables need event handlers setup
    postInitialize() {
        this.initializeEventHandlers();
    }

    // Abstract method for event handlers
    initializeEventHandlers() {
        // To be implemented by subclasses that need event handling
    }

    // Common change tracking functionality
    trackChanges(hasChanges) {
        this.hasChanges = hasChanges;
        this.toggleBulkActions();
    }

    toggleBulkActions() {
        // Default implementation - subclasses should override this
        console.warn('toggleBulkActions() should be overridden by subclass');
    }
}
