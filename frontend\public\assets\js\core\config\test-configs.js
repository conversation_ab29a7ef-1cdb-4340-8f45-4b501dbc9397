/*
 * Test Configuration
 *
 * 1. FUNCTIONAL SETTINGS - Global technical configurations (form lengths, navigation delays, etc.)
 * 2. UI TEXT - Global UI text with bilingual support (common buttons, navigation, etc.)
 * 3. MESSAGES - Cross-cutting messages organized by type and category
 * 
 */

export const TEST_CONFIGS = {
    // ===== 1. FUNCTIONAL SETTINGS =====
    // Test-specific technical configurations
    // Form lengths and navigation delays use GLOBAL_CONFIGS standards

    // ===== 2. UI TEXT (Bilingual) =====
    // Test UI text organized by component/feature
    ui: {
        // Test type table configurations
        testTypeTable: {
            containerId: 'test-types-table-container',
            tableId: 'test-types-table',
            entityTerms: {
                en: 'test types',
                fr: 'types de tests'
            },
            manage: {
                addButton: {
                    text: { en: 'Add New Test', fr: 'Ajouter un nouveau test' },
                    urlPattern: '/{lang}/create-lab-test.html'
                }
            },

            tableSchema: {
                columns: [
                    {
                        key: 'name',
                        header: { en: 'Test Name', fr: 'Nom du test' },
                        formatter: 'safeText'
                    },
                    {
                        key: 'description',
                        header: { en: 'Test Description', fr: 'Description du test' },
                        formatter: 'safeText'
                    },
                    {
                        key: 'dateModified',
                        header: { en: 'Date Modified', fr: 'Date de modification' },
                        accessor: (row) => row.updated_at || row.created_at,
                        formatter: 'formatDateWithFallback'
                    },
                    {
                        key: 'is_active',
                        header: { en: 'Status', fr: 'Statut' },
                        formatter: 'testStatus'
                    }
                ],
                defaultOptions: {
                    order: [[2, "desc"]]
                }
            }
        },

        // Field terms for global template substitution (form validation messages)
        fieldTerms: {
            name: {
                en: 'test name',
                fr: 'nom du test'
            },
            description: {
                en: 'test description',
                fr: 'description du test'
            }
        }
    },

    // ===== 3. MESSAGES (Bilingual) =====
    // Test-specific messages use global templates
    // Most messages use global templates with entity substitution:
    // - Required fields: global.messages.errors.templates.fieldRequired (uses {field} placeholder)
    // - Length validation: global.messages.errors.templates.fieldTooLong (uses {field} placeholder)
    // - CRUD operations: global.messages.success/errors.templates.entity* (uses {entity} placeholder)
    // - Duplicate names: global.messages.warnings.templates.duplicateName (uses {entity} placeholder)
    // - Cancelled operations: global.messages.info.common.cancelled
};
