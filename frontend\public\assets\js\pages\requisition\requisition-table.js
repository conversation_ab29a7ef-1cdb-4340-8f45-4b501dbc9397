/*
 * Requisition Table
 * Read-only table for displaying requisition information
 */
import { ReadOnlyTable } from '../../core/components/table-base.js';
import { showMessage } from '../../core/helpers/message-helpers.js';

import { RequisitionApi } from '../../core/services/requisition-api.js';
import { REQUISITION_CONFIGS } from '../../core/config/requisition-configs.js';
import { GLOBAL_CONFIGS } from '../../core/config/global-configs.js';

const MESSAGE_CONFIG_MAPS = {
    global: GLOBAL_CONFIGS,
    requisition: REQUISITION_CONFIGS
};

class RequisitionTable extends ReadOnlyTable {
    constructor(options = {}) {
        super({
            containerId: options.containerId || REQUISITION_CONFIGS.ui.requisitionTable.containerId,
            tableId: options.tableId || REQUISITION_CONFIGS.ui.requisitionTable.tableId,
            entityConfig: {
                ...REQUISITION_CONFIGS.ui.requisitionTable,
                entityTerms: REQUISITION_CONFIGS.ui.requisitionTable.entityTerms
            },
            messageConfigMaps: MESSAGE_CONFIG_MAPS
        });

        // Store user info for role-based column filtering
        this.userInfo = options.userInfo || null;

        // Queue management for concurrent table creation
        this.initializationQueue = [];
        this.isInitializing = false;
    }

    // Override create method to add queue management
    async create(config) {
        return new Promise((resolve, reject) => {
            // Add to queue with promise handlers
            this.initializationQueue.push({
                config: config,
                resolve: resolve,
                reject: reject
            });

            // Start processing queue
            this.processQueue();
        });
    }

    async processQueue() {
        if (this.isInitializing || this.initializationQueue.length === 0) {
            return;
        }

        this.isInitializing = true;

        // Process next task in queue
        const task = this.initializationQueue.shift();

        try {
            await this.initializeTable(task.config);
            task.resolve();
            // Process next task
            this.isInitializing = false;
            this.processQueue();
        } catch (error) {
            // Use stable identifier instead of potentially undefined title
            const tableIdentifier = task.config.tableId || this.tableId || 'unknown-table';
            console.error(`Failed to initialize table "${tableIdentifier}":`, error);
            task.reject(error);
            // Continue processing queue even if one fails
            this.isInitializing = false;
            this.processQueue();
        }
    }

    async initializeTable(config) {
        // Store current config for access by other methods
        this.currentConfig = config;

        // Use parent class create method
        return await super.create(config);
    }

    // Handle errors with standard GC error display
    handleError($container, error) {
        $container.empty();
        console.error(`Failed to create requisition table "${this.tableId}":`, error);
        showMessage('#page-error-container', 'global.messages.errors.server', 'connectionError', MESSAGE_CONFIG_MAPS);
    }

    // Fetch requisitions data from API
    async fetchData() {
        const config = this.currentConfig;



        if (!config || !config.apiStatusQuery) {
            throw new Error('Missing apiStatusQuery in configuration');
        }

        const requisitions = await new Promise((resolve, reject) => {
            RequisitionApi.getRequisitions(config.apiStatusQuery, config.labId, config.role)
                .done(resolve)
                .fail(reject);
        });

        // Backend now provides data sorted by timestamp (newest first)
        return requisitions || [];
    }

    // Override create method to add queue management
    async create(config) {
        return new Promise((resolve, reject) => {
            // Add to queue with promise handlers
            this.initializationQueue.push({
                config: config,
                resolve: resolve,
                reject: reject
            });

            // Start processing queue
            this.processQueue();
        });
    }

    // Process initialization queue to handle concurrent table creation
    async processQueue() {
        if (this.isInitializing || this.initializationQueue.length === 0) {
            return;
        }

        this.isInitializing = true;

        // Process next task in queue
        const task = this.initializationQueue.shift();

        try {
            await this.initializeTable(task.config);
            task.resolve();
            // Process next task
            this.isInitializing = false;
            this.processQueue();
        } catch (error) {
            // Use stable identifier instead of potentially undefined title
            const tableIdentifier = task.config.tableId || this.tableId || 'unknown-table';
            console.error(`Failed to initialize table "${tableIdentifier}":`, error);
            task.reject(error);
            // Continue processing queue even if one fails
            this.isInitializing = false;
            this.processQueue();
        }
    }

    async initializeTable(config) {
        // Store current config for access by other methods
        this.currentConfig = config;

        // Use parent class create method
        return await super.create(config);
    }

    // Override getVisibleColumns to handle role-based filtering
    getVisibleColumns() {
        const columns = this.entityConfig.tableSchema.columns;

        // Filter columns based on user role
        return columns.filter(column => {
            // Lab column is only shown to scientists
            if (column.key === 'lab_name') {
                return this.userInfo?.role === 'scientist';
            }
            return true;
        });
    }

    // Override getTableOptions to handle dynamic column ordering
    getTableOptions() {
        const visibleColumns = this.getVisibleColumns();

        // Find the index of dateModified column in visible columns
        const dateModifiedIndex = visibleColumns.findIndex(col => col.key === 'dateModified');

        if (dateModifiedIndex >= 0) {
            return {
                order: [[dateModifiedIndex, "desc"]]
            };
        }

        return {};
    }

}

// Create and export a singleton instance to ensures that the same queue is used throughout the application
export const requisitionTable = new RequisitionTable();
